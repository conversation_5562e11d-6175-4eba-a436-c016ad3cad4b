/**
 * Lien Audio → Caméra
 *
 * Automation intelligente qui contrôle la visibilité des caméras selon les niveaux audio des micros.
 *
 * Fonctionnalités :
 * - Activation quasi-instantanée des caméras (100ms)
 * - Maintien minimum des caméras actives (4s)
 * - Mode plein écran automatique lors de discussions multiples (3s)
 * - Protection contre les changements trop rapides (3s minimum)
 * - Support des micros multiples par caméra
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, ModuleEvent } from '../interfaces/module.interface';
import { audioToCameraConfig } from '../../config';
import type { MicToCameraMapping } from '../../config/defaults/audio-to-camera.default';
import { AudioSourceConfig } from '../../obs/obs-observer.service';

interface AudioEventData {
    inputName: string;
    [key: string]: any;
}

interface AudioState {
    /** Niveau audio actuel (high/low) */
    level: 'high' | 'low';
    /** Timestamp de la dernière activation */
    lastActivated: number;
    /** Timestamp de la dernière désactivation */
    lastDeactivated: number;
}

interface CameraState {
    /** Source caméra actuellement visible */
    activeCamera: string | null;
    /** Timestamp de l'activation de la caméra active */
    activatedAt: number;
    /** Timer pour le délai d'activation */
    activationTimer: NodeJS.Timeout | null;
    /** Timer pour le délai de masquage */
    hideTimer: NodeJS.Timeout | null;
    /** Mode plein écran actif */
    isFullscreen: boolean;
    /** Timer pour le mode plein écran */
    fullscreenTimer: NodeJS.Timeout | null;
    /** Timestamp du dernier changement de caméra */
    lastChangeTime: number;
    /** Timer pour réévaluer après le hold period */
    holdPeriodTimer: NodeJS.Timeout | null;
    /** Timer pour réévaluer après minChangeIntervalMs */
    minChangeIntervalTimer: NodeJS.Timeout | null;
}

export class AudioToCameraLink implements IModuleLink {
    public readonly name = 'audio-to-camera';
    public readonly description = 'Contrôle la visibilité des caméras selon les niveaux audio des micros';
    public readonly enabled: boolean;

    private obsModule?: IModule;

    // États internes
    private audioStates = new Map<string, AudioState>();
    private cameraState: CameraState = {
        activeCamera: null,
        activatedAt: 0,
        activationTimer: null,
        hideTimer: null,
        isFullscreen: false,
        fullscreenTimer: null,
        lastChangeTime: 0,
        holdPeriodTimer: null,
        minChangeIntervalTimer: null,
    };

    constructor(private linkConfig: LinkConfig) {
        this.enabled = linkConfig.enabled && audioToCameraConfig?.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        await Promise.resolve();

        if (!this.enabled) {
            console.log('[AudioToCameraLink] Link disabled - skipping initialization');
            return;
        }

        // Récupérer le module OBS
        this.obsModule = modules.get('obs');
        if (!this.obsModule) {
            console.warn('[AudioToCameraLink] OBS module not found - link will not function');
            return;
        }

        // Initialiser les états audio pour chaque micro configuré
        audioToCameraConfig.micToCameraMapping.forEach((mapping: MicToCameraMapping) => {
            this.audioStates.set(mapping.micName, {
                level: 'low',
                lastActivated: 0,
                lastDeactivated: 0,
            });
        });

        // Configurer les sources audio à écouter dans l'OBS observer
        this.configureAudioSources();

        // Écouter les événements audio du module OBS
        this.setupOBSListeners();

        console.log(`[AudioToCameraLink] Initialized with ${audioToCameraConfig.micToCameraMapping.length} mic-to-camera mappings`);
        if (audioToCameraConfig.debug.verbose) {
            console.log('[AudioToCameraLink] Configuration:', {
                targetScene: audioToCameraConfig.targetScene,
                audioThresholdDb: audioToCameraConfig.audioThresholdDb,
                activationDelayMs: audioToCameraConfig.activationDelayMs,
                holdDurationMs: audioToCameraConfig.holdDurationMs,
                mappings: audioToCameraConfig.micToCameraMapping,
            });
        }
    }

    async cleanup(): Promise<void> {
        await Promise.resolve();

        // Nettoyer tous les timers
        this.clearAllTimers();

        console.log('[AudioToCameraLink] Cleaned up');
    }

    // ============================================================================
    // CONFIGURATION DES SOURCES AUDIO
    // ============================================================================

    /**
     * Configure les sources audio à écouter dans l'OBS observer
     */
    private configureAudioSources(): void {
        if (!this.obsModule) return;

        // Créer la liste des sources audio à partir du mapping
        const audioSources: AudioSourceConfig[] = audioToCameraConfig.micToCameraMapping.map((mapping: MicToCameraMapping) => ({
            inputName: mapping.micName,
            channel: 'left' as const, // Par défaut, utiliser le canal gauche
        }));

        // Configurer les sources dans l'OBS observer
        // eslint-disable-next-line
        (this.obsModule as any).observerService?.setAudioSources?.(audioSources);

        if (audioToCameraConfig.debug.verbose) {
            console.log(
                '[AudioToCameraLink] Configured audio sources:',
                audioSources.map((s) => `${s.inputName} (${s.channel})`),
            );
        }
    }

    // ============================================================================
    // GESTION DES ÉVÉNEMENTS AUDIO
    // ============================================================================

    private setupOBSListeners(): void {
        if (!this.obsModule) return;

        const eventCallback = (event: ModuleEvent) => {
            const audioData = event.data as AudioEventData;
            if (event.type === 'input_volume_HIGH' && audioData?.inputName) {
                this.handleMicActivated(audioData.inputName);
            } else if (event.type === 'input_volume_LOW' && audioData?.inputName) {
                this.handleMicDeactivated(audioData.inputName);
            }
        };

        this.obsModule.onEvent(eventCallback);
    }

    private handleMicActivated(micName: string): void {
        // Trouver la caméra associée à ce micro
        const mapping = audioToCameraConfig.micToCameraMapping.find((m: MicToCameraMapping) => m.micName === micName);
        if (!mapping) return;

        const cameraSource = mapping.cameraSource;
        const audioState = this.audioStates.get(micName);
        if (!audioState) return;

        // Mettre à jour l'état audio
        audioState.level = 'high';
        audioState.lastActivated = Date.now();
        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic activated: ${micName} → ${cameraSource}`);
        }

        // Annuler le timer de masquage s'il existe
        if (this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
            this.cameraState.hideTimer = null;
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    private handleMicDeactivated(micName: string): void {
        // Mettre à jour l'état audio
        const audioState = this.audioStates.get(micName);
        if (audioState) {
            audioState.level = 'low';
            audioState.lastDeactivated = Date.now();
        }

        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic deactivated: ${micName}`);
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    // ============================================================================
    // LOGIQUE PRINCIPALE DE DÉCISION
    // ============================================================================

    /**
     * Évaluer l'état des caméras et décider de l'action à prendre
     */
    private evaluateCameraState(): void {
        // Grouper les micros actifs par caméra
        const activeCameraGroups = this.getActiveCameraGroups();

        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Active camera groups:`, Array.from(activeCameraGroups.entries()));
        }

        // Gérer le mode plein écran
        this.handleFullscreenMode(activeCameraGroups);

        // Si on est en mode plein écran, ne pas changer de caméra
        if (this.cameraState.isFullscreen) {
            return;
        }

        // Annuler le timer de masquage s'il y a des micros actifs
        if (activeCameraGroups.size > 0 && this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
            this.cameraState.hideTimer = null;
        }

        if (activeCameraGroups.size === 0) {
            // Aucun micro actif - programmer le masquage si configuré
            if (audioToCameraConfig.fallbackBehavior.hideAllWhenInactive) {
                if (this.cameraState.hideTimer) {
                    clearTimeout(this.cameraState.hideTimer);
                }

                this.cameraState.hideTimer = setTimeout(() => {
                    void this.hideAllCameras();
                }, audioToCameraConfig.fallbackBehavior.hideDelayMs);
            }
        } else if (activeCameraGroups.size === 1) {
            // Une seule caméra a des micros actifs
            const [cameraSource] = activeCameraGroups.keys();

            // Vérifier les contraintes de timing
            const now = Date.now();

            // 1. Vérifier la durée de maintien si une autre caméra est déjà active
            if (this.cameraState.activeCamera && this.cameraState.activeCamera !== cameraSource && now - this.cameraState.activatedAt < audioToCameraConfig.holdDurationMs) {
                if (audioToCameraConfig.debug.verbose) {
                    console.log(`[AudioToCameraLink] Camera ${this.cameraState.activeCamera} still in hold period, ignoring switch to ${cameraSource}`);
                }

                // Programmer une réévaluation après la fin du hold period
                this.scheduleHoldPeriodReevaluation();
                return;
            }

            // 2. Vérifier le délai minimum entre changements
            if (this.cameraState.lastChangeTime > 0 && now - this.cameraState.lastChangeTime < audioToCameraConfig.minChangeIntervalMs) {
                if (audioToCameraConfig.debug.verbose) {
                    console.log(`[AudioToCameraLink] Too soon since last change, ignoring switch to ${cameraSource}`);
                }

                // Programmer une réévaluation après minChangeIntervalMs
                this.scheduleMinChangeIntervalReevaluation();
                return;
            }

            // Activer la caméra avec délai ou immédiatement selon le contexte
            if (this.cameraState.activeCamera === null) {
                // Pas de caméra active, utiliser le délai d'activation
                if (this.cameraState.activationTimer) {
                    clearTimeout(this.cameraState.activationTimer);
                }

                this.cameraState.activationTimer = setTimeout(() => {
                    void this.activateCamera(cameraSource);
                }, audioToCameraConfig.activationDelayMs);
            } else {
                // Changement de caméra, activation immédiate
                void this.activateCamera(cameraSource);
            }
        }
        // Si activeCameraGroups.size > 1, la logique du mode plein écran s'en occupe
    }

    // ============================================================================
    // CONTRÔLE DES CAMÉRAS
    // ============================================================================

    private async activateCamera(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        try {
            // Masquer toutes les caméras d'abord
            const allCameraSources = audioToCameraConfig.micToCameraMapping.map((m: MicToCameraMapping) => m.cameraSource);
            // eslint-disable-next-line
            await (this.obsModule as any).hideAllSources(audioToCameraConfig.targetScene, allCameraSources);

            // Afficher la caméra sélectionnée
            // eslint-disable-next-line
            await (this.obsModule as any).setSourceVisibility(audioToCameraConfig.targetScene, cameraSource, true);

            // Mettre à jour l'état
            const now = Date.now();
            this.cameraState.activeCamera = cameraSource;
            this.cameraState.activatedAt = now;
            this.cameraState.lastChangeTime = now;
            this.cameraState.activationTimer = null;

            console.log(`[AudioToCameraLink] Camera activated: ${cameraSource} in scene ${audioToCameraConfig.targetScene}`);
        } catch (error) {
            console.error(`[AudioToCameraLink] Failed to activate camera ${cameraSource}:`, error);
        }
    }

    private async hideAllCameras(): Promise<void> {
        if (!this.obsModule) return;

        try {
            const allCameraSources = audioToCameraConfig.micToCameraMapping.map((m: MicToCameraMapping) => m.cameraSource);
            // eslint-disable-next-line
            await (this.obsModule as any).hideAllSources(audioToCameraConfig.targetScene, allCameraSources);

            this.cameraState.activeCamera = null;
            this.cameraState.activatedAt = 0;
            this.cameraState.lastChangeTime = Date.now();
            this.cameraState.hideTimer = null;

            console.log(`[AudioToCameraLink] All cameras hidden in scene ${audioToCameraConfig.targetScene} (fullscreen mode)`);
        } catch (error) {
            console.error('[AudioToCameraLink] Failed to hide cameras:', error);
        }
    }

    // ============================================================================
    // MODE PLEIN ÉCRAN (FULLSCREEN)
    // ============================================================================

    /**
     * Grouper les micros actifs par caméra associée
     */
    private getActiveCameraGroups(): Map<string, string[]> {
        const cameraGroups = new Map<string, string[]>();

        // Parcourir tous les micros actifs
        for (const [micName, audioState] of this.audioStates.entries()) {
            if (audioState.level === 'high') {
                // Trouver la caméra associée à ce micro
                const mapping = audioToCameraConfig.micToCameraMapping.find((m: MicToCameraMapping) => m.micName === micName);
                if (mapping) {
                    const cameraSource = mapping.cameraSource;
                    if (!cameraGroups.has(cameraSource)) {
                        cameraGroups.set(cameraSource, []);
                    }
                    cameraGroups.get(cameraSource)!.push(micName);
                }
            }
        }

        return cameraGroups;
    }

    /**
     * Vérifier si on doit passer en mode plein écran
     */
    private shouldEnterFullscreen(activeCameraGroups: Map<string, string[]>): boolean {
        if (!audioToCameraConfig.fullscreenMode.enabled) {
            return false;
        }

        // Compter le nombre de caméras différentes qui ont des micros actifs
        const activeCameraCount = activeCameraGroups.size;

        return activeCameraCount >= audioToCameraConfig.fullscreenMode.minActiveMics;
    }

    /**
     * Gérer le mode plein écran avec logique plus stable
     */
    private handleFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        const shouldBeFullscreen = this.shouldEnterFullscreen(activeCameraGroups);

        if (shouldBeFullscreen && !this.cameraState.isFullscreen) {
            // Démarrer le timer pour le mode plein écran si pas déjà démarré
            if (!this.cameraState.fullscreenTimer) {
                if (audioToCameraConfig.debug.verbose) {
                    console.log(`[AudioToCameraLink] Multiple cameras active, starting fullscreen timer (${audioToCameraConfig.fullscreenMode.multiMicDurationMs}ms)`);
                }

                this.cameraState.fullscreenTimer = setTimeout(() => {
                    // Vérifier à nouveau si on devrait toujours être en plein écran
                    const currentActiveCameraGroups = this.getActiveCameraGroups();
                    if (this.shouldEnterFullscreen(currentActiveCameraGroups)) {
                        if (audioToCameraConfig.debug.verbose) {
                            console.log(`[AudioToCameraLink] Fullscreen timer expired, entering fullscreen mode`);
                        }
                        this.enterFullscreenMode();
                    } else {
                        if (audioToCameraConfig.debug.verbose) {
                            console.log(`[AudioToCameraLink] Fullscreen timer expired but conditions no longer met, canceling`);
                        }
                        this.cameraState.fullscreenTimer = null;
                    }
                }, audioToCameraConfig.fullscreenMode.multiMicDurationMs);
            }
        } else if (!shouldBeFullscreen) {
            // Ne pas annuler le timer immédiatement - laisser une chance au mode plein écran
            // Le timer se chargera de vérifier les conditions à son expiration

            // Sortir du mode plein écran si on y est et qu'on ne devrait plus y être
            if (this.cameraState.isFullscreen) {
                // Attendre un peu avant de sortir pour éviter les basculements rapides
                setTimeout(() => {
                    const currentActiveCameraGroups = this.getActiveCameraGroups();
                    if (!this.shouldEnterFullscreen(currentActiveCameraGroups) && this.cameraState.isFullscreen) {
                        if (audioToCameraConfig.debug.verbose) {
                            console.log(`[AudioToCameraLink] Multiple cameras no longer active, exiting fullscreen mode`);
                        }
                        this.exitFullscreenMode(currentActiveCameraGroups);
                    }
                }, 500); // Délai de 500ms pour stabiliser
            }
        }
    }

    /**
     * Entrer en mode plein écran
     */
    private enterFullscreenMode(): void {
        if (this.cameraState.isFullscreen) return;

        this.cameraState.isFullscreen = true;
        this.cameraState.fullscreenTimer = null; // Timer terminé

        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Entering fullscreen mode (multiple cameras active)`);
        }

        // Masquer toutes les caméras pour révéler CAM1
        void this.hideAllCameras();
    }

    /**
     * Sortir du mode plein écran
     */
    private exitFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        if (!this.cameraState.isFullscreen) return;

        this.cameraState.isFullscreen = false;

        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Exiting fullscreen mode`);
        }

        // Activer la caméra appropriée selon les micros encore actifs
        if (activeCameraGroups.size === 1) {
            const [cameraSource] = activeCameraGroups.keys();
            void this.activateCamera(cameraSource);
        }
    }

    // ============================================================================
    // GESTION DES TIMERS DE RÉÉVALUATION
    // ============================================================================

    /**
     * Programmer une réévaluation après la fin du hold period
     */
    private scheduleHoldPeriodReevaluation(): void {
        // Annuler le timer précédent s'il existe
        if (this.cameraState.holdPeriodTimer) {
            clearTimeout(this.cameraState.holdPeriodTimer);
        }

        // Calculer le temps restant du hold period
        const now = Date.now();
        const holdEndTime = this.cameraState.activatedAt + audioToCameraConfig.holdDurationMs;
        const remainingTime = Math.max(0, holdEndTime - now);

        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Scheduling reevaluation in ${remainingTime}ms (after hold period)`);
        }

        // Programmer la réévaluation
        this.cameraState.holdPeriodTimer = setTimeout(() => {
            if (audioToCameraConfig.debug.verbose) {
                console.log(`[AudioToCameraLink] Hold period ended, reevaluating camera state`);
            }
            this.cameraState.holdPeriodTimer = null;
            this.evaluateCameraState();
        }, remainingTime + 100); // +100ms pour être sûr que le hold period est fini
    }

    /**
     * Programmer une réévaluation après minChangeIntervalMs
     */
    private scheduleMinChangeIntervalReevaluation(): void {
        // Annuler le timer précédent s'il existe
        if (this.cameraState.minChangeIntervalTimer) {
            clearTimeout(this.cameraState.minChangeIntervalTimer);
        }

        // Calculer le temps restant du délai minimum
        const now = Date.now();
        const intervalEndTime = this.cameraState.lastChangeTime + audioToCameraConfig.minChangeIntervalMs;
        const remainingTime = Math.max(0, intervalEndTime - now);

        if (audioToCameraConfig.debug.verbose) {
            console.log(`[AudioToCameraLink] Scheduling reevaluation in ${remainingTime}ms (after min change interval)`);
        }

        // Programmer la réévaluation
        this.cameraState.minChangeIntervalTimer = setTimeout(() => {
            if (audioToCameraConfig.debug.verbose) {
                console.log(`[AudioToCameraLink] Min change interval ended, reevaluating camera state`);
            }
            this.cameraState.minChangeIntervalTimer = null;
            this.evaluateCameraState();
        }, remainingTime + 100); // +100ms pour être sûr que l'intervalle est fini
    }

    // ============================================================================
    // UTILITAIRES
    // ============================================================================

    /**
     * Nettoyer tous les timers actifs
     */
    private clearAllTimers(): void {
        if (this.cameraState.activationTimer) {
            clearTimeout(this.cameraState.activationTimer);
            this.cameraState.activationTimer = null;
        }
        if (this.cameraState.hideTimer) {
            clearTimeout(this.cameraState.hideTimer);
            this.cameraState.hideTimer = null;
        }
        if (this.cameraState.fullscreenTimer) {
            clearTimeout(this.cameraState.fullscreenTimer);
            this.cameraState.fullscreenTimer = null;
        }
        if (this.cameraState.holdPeriodTimer) {
            clearTimeout(this.cameraState.holdPeriodTimer);
            this.cameraState.holdPeriodTimer = null;
        }
        if (this.cameraState.minChangeIntervalTimer) {
            clearTimeout(this.cameraState.minChangeIntervalTimer);
            this.cameraState.minChangeIntervalTimer = null;
        }
    }
}
