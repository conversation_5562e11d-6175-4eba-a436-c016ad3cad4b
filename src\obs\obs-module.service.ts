/**
 * Module OBS autonome - Version nettoyée
 * Responsabilité : Connexion WebSocket et orchestration des services OBS
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import OBSWebSocket, { EventSubscription } from 'obs-websocket-js';
import { BaseModule } from '../core/base/base-module';
import { obsConfig, modulesConfig } from '../config';
import { OBSObserverService } from './obs-observer.service';
import { OBSControllerService } from './obs-controller.service';

@Injectable()
export class OBSModuleService extends BaseModule implements OnModuleInit, OnModuleDestroy {
    private obs: OBSWebSocket;
    private reconnectTimer?: NodeJS.Timeout;
    private pingTimer?: NodeJS.Timeout;
    private isConnecting = false;
    private readonly config = obsConfig;

    // Services spécialisés - publics pour accès par les links
    public observer: OBSObserverService;
    public controller: OBSControllerService;

    constructor() {
        super('obs', modulesConfig.obs.enabled);
        this.obs = new OBSWebSocket();
        this.observer = new OBSObserverService();
        this.controller = new OBSControllerService();
        this.setupServices();
        this.log('OBS module created', { enabled: this.enabled });
    }

    private setupServices(): void {
        // Configurer le callback pour les événements de l'observer
        this.observer.setEventCallback((type: string, data: any) => {
            // Émettre l'événement vers les autres modules
            this.emitEvent(type, data);
        });

        // Configurer les listeners de connexion OBS
        this.setupConnectionListeners();
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        if (this.config.autoConnect) {
            await this.connect();
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping OBS module...');

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }

        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = undefined;
        }

        if (this.obs.identified) {
            await this.obs.disconnect();
        }

        this.updateConnectionStatus({ connected: false });
        this.log('OBS module stopped');
    }

    // === MÉTHODES PUBLIQUES - CONTRÔLE OBS ===

    /**
     * Changer de scène OBS (exemple de contrôle)
     */
    async changeScene(sceneName: string): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.changeScene(this.obs, sceneName);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to change scene to "${sceneName}"`);
            throw error;
        }
    }

    /**
     * Contrôler la visibilité d'une source dans une scène
     */
    async setSourceVisibility(sceneName: string, sourceName: string, visible: boolean): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.setSourceVisibility(this.obs, sceneName, sourceName, visible);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to set visibility of "${sourceName}" in "${sceneName}"`);
            throw error;
        }
    }

    /**
     * Masquer toutes les sources spécifiées dans une scène
     */
    async hideAllSources(sceneName: string, sourceNames: string[]): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.hideAllSources(this.obs, sceneName, sourceNames);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to hide sources in "${sceneName}"`);
            throw error;
        }
    }

    // === MÉTHODES PRIVÉES ===

    private setupConnectionListeners(): void {
        this.obs.on('ConnectionOpened', () => {
            this.log('WebSocket connection opened');
        });

        this.obs.on('ConnectionClosed', () => {
            this.log('WebSocket connection closed');
            this.updateConnectionStatus({ connected: false });
            this.scheduleReconnect();
        });

        this.obs.on('ConnectionError', (error) => {
            this.log('WebSocket connection error:', error.message);
            this.updateConnectionStatus({ connected: false, error: error.message });
        });
    }

    private async connect(): Promise<void> {
        if (this.isConnecting || this.obs.identified) {
            return;
        }

        this.isConnecting = true;

        try {
            this.log(`Connecting to OBS at ${this.config.network.host}:${this.config.network.port}...`);

            const eventSubs = EventSubscription.General | EventSubscription.InputVolumeMeters;

            const url = `ws://${this.config.network.host}:${this.config.network.port}`;
            const connectionInfo = await this.obs.connect(url, this.config.network.password, { rpcVersion: 1, eventSubscriptions: eventSubs });

            this.log(`Connected to OBS WebSocket v${connectionInfo.obsWebSocketVersion} (RPC v${connectionInfo.negotiatedRpcVersion})`);

            this.updateConnectionStatus({ connected: true });
            this.startPing();

            // Configurer les listeners d'événements
            this.observer.setupEventListeners(this.obs);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), 'Failed to connect to OBS');
            this.scheduleReconnect();
        } finally {
            this.isConnecting = false;
        }
    }

    private startPing(): void {
        this.pingTimer = setInterval(() => {
            if (this.obs.identified) {
                this.obs.call('GetVersion').catch(() => {
                    this.log('Ping failed - connection may be lost');
                });
            }
        }, this.config.timing.pingInterval);
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        this.reconnectTimer = setTimeout(() => {
            this.log('Attempting to reconnect...');
            void this.connect();
        }, this.config.timing.reconnectInterval);
    }
}
